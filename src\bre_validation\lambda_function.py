import os
import json

from .validation_helper.validation_execution import ValidatorExecution
from ..common.aria_helper.boto3_utils import get_secret, trigger_lambda_response
from ..common.aria_helper.mongo_utils import Mongo
from ..common.aria_helper.aria_utils import ARIA

environment = os.environ['ENV']
database_name = os.environ['DATABASE_NAME']
aria_environment = os.environ['ARIA_ENVIRONMENT']
common_prefix = f"{os.environ['ENV']}-{os.environ['PROJECT_NAME']}"


class BreValidationHandler:
    def __init__(self, event):
        self.event = event
        # Parse the event body once and reuse it
        self.document = event['body'] if isinstance(event['body'], dict) else json.loads(event['body'])
        self.input_body = self.document
        self.app_id = self.document['app_id']
        self.document_id = self.document['id']
        self.current_status_id = self.document.get('aria_status', '')
        self.statuses = self.input_body.get('aria_status', self.current_status_id)
        self.ocr_groups = self.document.get('ocr_groups', [])
        self.request_response = self.input_body.get('request_response', False)

        self.mongo_client = Mongo(get_secret(common_prefix + '-mongodb_uri', return_json=False)).client
        self.validator_execution = ValidatorExecution()
        self.aria_secret = get_secret(secret_name=f'dev-Connector-aria_creds')
        self.validation_config = self.mongo_client[database_name]["validation_config"].find_one({
            # "app_id": self.app_id,
            "validation_type_key": "tag_titles"
        })

        if not self.validation_config:
            raise ValueError(f"No validation config found for app_id: {self.app_id}")

        if not self.app_id:
            raise ValueError("app_id is missing from the document")
    def post_to_aria(self, bre_response):
        """
        Post the BRE response to ARIA.
        Returns True if successful, raises exception if failed.
        Following old lambda pattern - only post if not request_response
        """
        try:
            if not self.request_response:
                aria = ARIA(
                    base_url=self.aria_secret[aria_environment]['url'],
                    request_token=self.aria_secret[aria_environment]['token']
                )

                aria.bre_reply(
                    app_id=self.app_id,
                    item_id=self.document_id,
                    bre_response=bre_response
                )
            return True
        except Exception as e:
            print(f"Failed to post to ARIA: {str(e)}")
            raise

    def run(self):
        try:
            # Perform validation
            print("Performing validation")
            print("document--------" ,self.document)
            print(self.validation_config)
            is_valid, validation_result = self.validator_execution.validate_data(self.document, self.validation_config)

            # Determine target status and exception based on validation result (following old lambda pattern)
            print("Validation result------------------", self.statuses)
            if is_valid:
                # If validation passes, use the current status (or could be configured to move to next status)
                next_status_id = self.statuses  # Keep current status for now
                aria_exception = ""
            else:
                # If validation fails, keep current status but set exception
                next_status_id = self.statuses  # Keep current status
                # Build exception message from validation errors (following old lambda pattern)
                validation_errors = validation_result.get('errors', [])
                aria_exception = f"Validation failed: {'; '.join(validation_errors)}" if validation_errors else "One or more fields require human intervention"

            # Build BRE response following old lambda pattern
            bre_response = {
                "aria_status": {"value": next_status_id},
                "aria_exception": {"value": aria_exception}
            }

            # Include the groups data (following old lambda pattern: self.ocr_groups[0]: self.parsed_fields[self.ocr_groups[0]])
            if validation_result and "groups" in validation_result:
                # Add each group's data to the BRE response (this is what updates ARIA)
                for group_name, group_data in validation_result["groups"].items():
                    bre_response[group_name] = group_data

            print("REQUEST RESPONSE", self.request_response)
            print("********* TO STATE ", next_status_id)

            self.post_to_aria(bre_response)
            print("bre_response-------------", bre_response)

            # Build the response body with the validation result data
            response_body = {
                "aria_status": {"value": next_status_id},
                "aria_exception": {"value": aria_exception}
            }

            # Add the groups data with bre_exceptions
            if validation_result and "groups" in validation_result:
                for group_name, group_data in validation_result["groups"].items():
                    response_body[group_name] = group_data

            # Add group_top_exceptions if present
            if validation_result and "group_top_exceptions" in validation_result:
                response_body["group_top_exceptions"] = validation_result["group_top_exceptions"]

            # Return response in the wanted format
            return {
                'statusCode': 200,
                'body': response_body
            }

        except Exception as e:
            print('Error: {}'.format(str(e)))
            return {
                'statusCode': 500,
                'body': json.dumps({'error': str(e)})
            }


def lambda_handler(event, context=None):
    print("event-data",event)
    if 'body' not in list(event.keys()):
        raise ValueError("body tag is missing on the dict. Skipping...")

    bre_validation_handler = BreValidationHandler(event)
    return bre_validation_handler.run()
