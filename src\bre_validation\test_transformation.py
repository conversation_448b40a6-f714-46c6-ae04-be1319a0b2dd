#!/usr/bin/env python3
"""
Test script to verify the ARIA format transformation
"""
import json
import sys
import os

# Add the current directory to the path so we can import the lambda function
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_transformation():
    """Test the transformation from current format to wanted format"""
    
    # Load the test data
    with open('even_body.json', 'r') as f:
        event_body = eval(f.read())  # Using eval since it's Python dict format
    
    with open('current_bre_response.json', 'r') as f:
        current_response = eval(f.read())  # Using eval since it's Python dict format
    
    with open('wanted_way.json', 'r') as f:
        wanted_format = json.load(f)
    
    print("=== EVENT BODY ===")
    print(json.dumps(event_body, indent=2)[:500] + "...")
    
    print("\n=== CURRENT BRE RESPONSE ===")
    print(json.dumps(current_response, indent=2)[:500] + "...")
    
    print("\n=== WANTED FORMAT ===")
    print(json.dumps(wanted_format, indent=2)[:500] + "...")
    
    # Create a mock BreValidationHandler to test the transformation
    class MockBreValidationHandler:
        def __init__(self):
            self.document_id = event_body['body']['id']
            self.app_id = event_body['body']['app_id']
            self.document = event_body['body']
            self.statuses = {}  # Mock statuses
        
        def build_aria_format_response(self, validation_result, next_status_id, aria_exception, is_valid):
            """
            Build response in the ARIA API format matching wanted_way.json structure
            """
            # Build the main data structure
            response_data = {
                "data": {
                    "type": "workItem",
                    "id": self.document_id,
                    "attributes": {
                        "id": self.document_id,
                        "flag": "first",
                        "fields": {
                            "aria_created": self.document.get('aria_created', ''),
                            "aria_last_update": self.document.get('aria_last_update', ''),
                            "aria_exception": aria_exception
                        },
                        "groups": {},
                        "exceptions": {}
                    },
                    "relationships": {
                        "user": {
                            "data": None
                        },
                        "status": {
                            "data": {
                                "id": str(next_status_id),
                                "type": "configStatus"
                            }
                        },
                        "app": {
                            "data": {
                                "id": str(self.app_id),
                                "type": "app"
                            }
                        }
                    }
                }
            }
            
            # Add groups data if validation result contains groups
            if validation_result and "groups" in validation_result:
                for group_name, group_data in validation_result["groups"].items():
                    # Transform group data to match wanted format
                    transformed_group = {
                        "ocr_data": group_data.get("ocr_data", {}),
                        "bre_exceptions": group_data.get("bre_exceptions", {}),
                        "fields": {},
                        "display": True
                    }
                    
                    # Transform fields to match wanted format
                    if "fields" in group_data:
                        for field_name, field_data in group_data["fields"].items():
                            if isinstance(field_data, dict):
                                transformed_group["fields"][field_name] = {
                                    "value": field_data.get("value", ""),
                                    "display": True,
                                    "edit": True,
                                    "confidence": field_data.get("confidence"),
                                    "coordinates": field_data.get("coordinates", {})
                                }
                            else:
                                # Handle simple field values
                                transformed_group["fields"][field_name] = {
                                    "value": field_data,
                                    "display": True,
                                    "edit": True,
                                    "confidence": None,
                                    "coordinates": {}
                                }
                    
                    response_data["data"]["attributes"]["groups"][group_name] = transformed_group
            
            return response_data
    
    # Test the transformation
    handler = MockBreValidationHandler()
    
    # Create mock validation result from current response
    validation_result = {"groups": current_response}
    
    # Remove the top-level fields that are not groups
    groups_only = {}
    for key, value in current_response.items():
        if key not in ['aria_status', 'aria_exception'] and isinstance(value, dict) and 'fields' in value:
            groups_only[key] = value
    
    validation_result = {"groups": groups_only}
    
    transformed = handler.build_aria_format_response(
        validation_result, 
        "4628e2f0-eaec-4dde-adf4-01149ececcab", 
        "One or more fields require human intervention", 
        False
    )
    
    print("\n=== TRANSFORMED RESULT ===")
    print(json.dumps(transformed, indent=2))
    
    print("\n=== COMPARISON ===")
    print("Wanted structure keys:", list(wanted_format.keys()))
    print("Transformed structure keys:", list(transformed.keys()))
    print("Wanted data keys:", list(wanted_format["data"].keys()))
    print("Transformed data keys:", list(transformed["data"].keys()))

if __name__ == "__main__":
    test_transformation()
