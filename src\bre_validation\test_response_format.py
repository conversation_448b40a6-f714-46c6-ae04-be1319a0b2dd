#!/usr/bin/env python3
"""
Test script to show the new response format
"""
import json

def show_response_format():
    """Show what the new response format will look like"""
    
    # Example validation result that would come from the validation engine
    validation_result = {
        "groups": {
            "driver_license": {
                "ocr_data": {
                    "link": "https://s3.example.com/...",
                    "ocr_status": 1,
                    "error": ""
                },
                "fields": {
                    "full_name": {
                        "value": "MEWAEL H HABTEGABIR",
                        "confidence": 0,
                        "coordinates": {}
                    },
                    "date_of_birth": {
                        "value": "06/19/1993",
                        "confidence": 0,
                        "coordinates": {}
                    }
                },
                "bre_exceptions": {
                    "full_name": "Full name does not match across documents",
                    "expiration_date": "Expiration date must be in the future"
                }
            },
            "mv-1": {
                "ocr_data": {
                    "link": "https://s3.example.com/...",
                    "ocr_status": 1,
                    "error": ""
                },
                "fields": {
                    "buyer_full_name": {
                        "value": "MEWAEL H HABTEGABIR",
                        "confidence": 0,
                        "coordinates": {}
                    },
                    "vin": {
                        "value": "SALWR2SU2MA776024",
                        "confidence": 0,
                        "coordinates": {}
                    }
                },
                "bre_exceptions": {
                    "buyer_full_name": "Buyer full name does not match across documents",
                    "vin": "VIN number does not match across documents"
                }
            }
        },
        "group_top_exceptions": {
            "cross_document_validation": "Multiple documents have inconsistent data"
        }
    }
    
    # This is what the new response will look like
    response_body = {
        "aria_status": {"value": "4628e2f0-eaec-4dde-adf4-01149ececcab"},
        "aria_exception": {"value": "One or more fields require human intervention"}
    }
    
    # Add the groups data with bre_exceptions
    for group_name, group_data in validation_result["groups"].items():
        response_body[group_name] = group_data
    
    # Add group_top_exceptions if present
    if "group_top_exceptions" in validation_result:
        response_body["group_top_exceptions"] = validation_result["group_top_exceptions"]
    
    final_response = {
        'statusCode': 200,
        'body': response_body
    }
    
    print("=== NEW RESPONSE FORMAT ===")
    print(json.dumps(final_response, indent=2))
    
    print("\n=== RESPONSE BODY KEYS ===")
    print("Keys in response body:", list(response_body.keys()))
    
    print("\n=== DRIVER LICENSE BRE EXCEPTIONS ===")
    print(json.dumps(response_body["driver_license"]["bre_exceptions"], indent=2))
    
    print("\n=== GROUP TOP EXCEPTIONS ===")
    print(json.dumps(response_body["group_top_exceptions"], indent=2))

if __name__ == "__main__":
    show_response_format()
